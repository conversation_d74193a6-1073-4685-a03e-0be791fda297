@echo off
echo Setting up KYC MCP with n8n...

REM Copy environment file
copy .env.docker .env

REM Build and start services
echo Building Docker containers...
docker-compose build

echo Starting services...
docker-compose up -d

echo Waiting for services to start...
timeout /t 30

echo Checking service status...
docker-compose ps

echo.
echo Setup complete!
echo.
echo Services:
echo - KYC API Server: http://localhost:8000
echo - KYC API Docs: http://localhost:8000/docs
echo - n8n Interface: http://localhost:5678
echo.
echo Default n8n credentials:
echo - Username: admin
echo - Password: admin123
echo.
echo To stop services: docker-compose down
echo To view logs: docker-compose logs -f
echo.
pause

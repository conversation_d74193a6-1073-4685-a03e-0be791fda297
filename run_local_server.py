#!/usr/bin/env python3
"""
Local HTTP server for KYC MCP without <PERSON><PERSON>
Run this to start the HTTP API server locally
"""

import os
import sys
import subprocess
import webbrowser
from pathlib import Path

def check_dependencies():
    """Check if required packages are installed"""
    try:
        import fastapi
        import uvicorn
        print("✓ FastAPI and Uvicorn are installed")
        return True
    except ImportError:
        print("✗ Missing dependencies. Installing...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "fastapi", "uvicorn[standard]"])
            print("✓ Dependencies installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("✗ Failed to install dependencies")
            return False

def load_environment():
    """Load environment variables"""
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✓ Environment variables loaded")
    except ImportError:
        print("! python-dotenv not available, using system environment")
    
    # Check API token
    api_token = os.getenv("SUREPASS_API_TOKEN")
    if api_token:
        print(f"✓ API token loaded (length: {len(api_token)})")
    else:
        print("✗ SUREPASS_API_TOKEN not found in environment")
        return False
    
    return True

def start_server():
    """Start the HTTP server"""
    if not check_dependencies():
        return False
    
    if not load_environment():
        print("\nPlease set your SUREPASS_API_TOKEN environment variable or update .env file")
        return False
    
    print("\n" + "="*50)
    print("Starting KYC HTTP Server...")
    print("="*50)
    
    # Get configuration
    host = os.getenv("HOST", "127.0.0.1")
    port = int(os.getenv("PORT", "8000"))
    
    print(f"Server will start at: http://{host}:{port}")
    print(f"API Documentation: http://{host}:{port}/docs")
    print(f"Health Check: http://{host}:{port}/health")
    print("\nPress Ctrl+C to stop the server")
    print("="*50)
    
    try:
        # Import and run the server
        import uvicorn
        uvicorn.run(
            "kyc_http_server:app",
            host=host,
            port=port,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n\nServer stopped by user")
    except Exception as e:
        print(f"\nError starting server: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print("KYC MCP Local Server Launcher")
    print("="*40)
    
    # Check if we're in the right directory
    if not Path("kyc_http_server.py").exists():
        print("✗ kyc_http_server.py not found in current directory")
        print("Please run this script from the KYC MCP project directory")
        return
    
    # Start the server
    start_server()

if __name__ == "__main__":
    main()

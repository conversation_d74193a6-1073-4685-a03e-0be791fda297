# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Logs
*.log
logs/

# Database files
*.db
*.sqlite
*.sqlite3

# Test files
test_*.py
*_test.py
tests/

# Documentation
*.md
docs/

# Temporary files
*.tmp
*.temp
.cache/

# Environment files (will be copied separately)
.env*

# Node modules (if any)
node_modules/

# Coverage reports
htmlcov/
.coverage
.pytest_cache/

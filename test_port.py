#!/usr/bin/env python3
"""Test port connectivity to SurePass API"""

import socket
import sys
import time

def test_port(host, port, timeout=10):
    """Test if a port is open on a host"""
    try:
        print(f"Testing connection to {host}:{port}...")
        
        # Create socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        
        # Try to connect
        start_time = time.time()
        result = sock.connect_ex((host, port))
        end_time = time.time()
        
        sock.close()
        
        if result == 0:
            print(f"✓ Port {port} is OPEN on {host} (took {end_time - start_time:.2f}s)")
            return True
        else:
            print(f"✗ Port {port} is CLOSED on {host} (error code: {result})")
            return False
            
    except socket.timeout:
        print(f"✗ Connection to {host}:{port} TIMED OUT after {timeout}s")
        return False
    except socket.gaierror as e:
        print(f"✗ DNS resolution failed for {host}: {e}")
        return False
    except Exception as e:
        print(f"✗ Error connecting to {host}:{port}: {e}")
        return False

def main():
    print("=== Port Connectivity Test ===\n")
    
    # Test the IP addresses we found
    hosts_to_test = [
        ("*************", "From ping test"),
        ("************", "From DNS resolution"),
        ("kyc-api.surepass.io", "Domain name")
    ]
    
    port = 443  # HTTPS port
    
    for host, description in hosts_to_test:
        print(f"\nTesting {description}: {host}")
        test_port(host, port, timeout=15)
    
    print("\n=== Additional Tests ===")
    
    # Test some common ports to see if outbound connections work at all
    print("\nTesting if outbound HTTPS connections work in general...")
    common_sites = [
        ("google.com", 443),
        ("github.com", 443),
        ("httpbin.org", 443)
    ]
    
    for site, port in common_sites:
        print(f"\nTesting {site}:{port}")
        if test_port(site, port, timeout=10):
            print("✓ General HTTPS connectivity works")
            break
    else:
        print("✗ No HTTPS connectivity - firewall may be blocking all outbound HTTPS")
    
    print("\n=== Test Complete ===")
    print("\nIf port 443 is closed to the SurePass servers but open to other sites,")
    print("then the SurePass servers may be blocking your IP or there's a routing issue.")

if __name__ == "__main__":
    main()

version: '3.8'

services:
  # KYC MCP Server
  kyc-server:
    build: .
    container_name: kyc-mcp-server
    ports:
      - "8000:8000"
    environment:
      - SUREPASS_API_TOKEN=${SUREPASS_API_TOKEN}
      - SUREPASS_BASE_URL=${SUREPASS_BASE_URL:-https://kyc-api.surepass.io/api/v1}
      - KYC_DATABASE_ENABLED=${KYC_DATABASE_ENABLED:-true}
      - KYC_DATABASE_URL=sqlite+aiosqlite:///data/kyc_data.db
      - PORT=8000
      - HOST=0.0.0.0
    volumes:
      - kyc_data:/app/data
    networks:
      - kyc_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # n8n Workflow Automation
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_USER:-admin}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_PASSWORD:-admin123}
      - N8N_HOST=${N8N_HOST:-localhost}
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://localhost:5678/
      - GENERIC_TIMEZONE=${TIMEZONE:-UTC}
      - N8N_LOG_LEVEL=info
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./n8n/workflows:/home/<USER>/.n8n/workflows
    networks:
      - kyc_network
    depends_on:
      kyc-server:
        condition: service_healthy
    restart: unless-stopped

  # Optional: PostgreSQL for n8n (recommended for production)
  postgres:
    image: postgres:13
    container_name: n8n-postgres
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-n8n}
      - POSTGRES_USER=${POSTGRES_USER:-n8n}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-n8n123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - kyc_network
    restart: unless-stopped
    profiles:
      - postgres

  # Optional: Redis for caching (recommended for production)
  redis:
    image: redis:7-alpine
    container_name: n8n-redis
    volumes:
      - redis_data:/data
    networks:
      - kyc_network
    restart: unless-stopped
    profiles:
      - redis

volumes:
  kyc_data:
    driver: local
  n8n_data:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  kyc_network:
    driver: bridge

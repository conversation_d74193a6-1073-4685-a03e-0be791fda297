# Claude + n8n + KYC MCP Integration Guide

## Overview
This setup allows <PERSON> to send requests to n8n workflows, which then communicate with your KYC MCP server running in Docker.

## Architecture
```
<PERSON> → n8n Webhook → KYC HTTP Server → SurePass API
```

## Setup Instructions

### 1. Start the Services
```cmd
setup.bat
```

### 2. Configure n8n Workflow
1. Open n8n at http://localhost:5678
2. Login with admin/admin123
3. Import the workflow from `n8n/workflows/kyc-pan-verification.json`
4. Activate the workflow

### 3. Get Webhook URL
In n8n, copy the webhook URL from the workflow (something like):
```
http://localhost:5678/webhook/verify-pan
```

### 4. Configure Claude
Update your Claude MCP configuration to use n8n instead of direct MCP:

```json
{
  "mcpServers": {
    "kyc-n8n": {
      "command": "python",
      "args": ["claude_n8n_bridge.py"],
      "env": {
        "N8N_WEBHOOK_BASE": "http://localhost:5678/webhook"
      }
    }
  }
}
```

## API Endpoints

### KYC Server Endpoints
- `GET /health` - Health check
- `GET /api/status` - API status
- `POST /api/verify/pan/basic` - Basic PAN verification
- `POST /api/verify/pan/comprehensive` - Comprehensive PAN verification
- `POST /api/verify/pan/kra` - PAN KRA verification

### n8n Webhook Endpoints
- `POST /webhook/verify-pan` - PAN verification via n8n

## Example Usage

### Direct API Call
```bash
curl -X POST http://localhost:8000/api/verify/pan/basic \
  -H "Content-Type: application/json" \
  -d '{"id_number": "**********"}'
```

### Via n8n Webhook
```bash
curl -X POST http://localhost:5678/webhook/verify-pan \
  -H "Content-Type: application/json" \
  -d '{"pan_number": "**********"}'
```

## Monitoring

### View Logs
```cmd
docker-compose logs -f kyc-server
docker-compose logs -f n8n
```

### Check Status
```cmd
docker-compose ps
```

## Troubleshooting

### Services Not Starting
```cmd
docker-compose down
docker-compose up -d --build
```

### Check API Connectivity
```cmd
curl http://localhost:8000/health
curl http://localhost:8000/api/status
```

### Reset n8n Data
```cmd
docker-compose down
docker volume rm kyc-verification-mcp_n8n_data
docker-compose up -d
```

#!/usr/bin/env python3
"""
Simple webhook server that mimics n8n functionality
This provides webhook endpoints that <PERSON> can call directly
"""

import os
import json
import asyncio
import logging
from typing import Dict, Any
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import httpx

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("webhook-server")

app = FastAPI(
    title="KYC Webhook Server",
    description="Simple webhook server for <PERSON> integration",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
KYC_SERVER_URL = os.getenv("KYC_SERVER_URL", "http://localhost:8000")

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "KYC Webhook Server",
        "endpoints": {
            "health": "/health",
            "webhook_pan_basic": "/webhook/pan/basic",
            "webhook_pan_comprehensive": "/webhook/pan/comprehensive",
            "webhook_pan_kra": "/webhook/pan/kra"
        }
    }

@app.get("/health")
async def health():
    """Health check"""
    return {"status": "healthy", "service": "webhook-server"}

@app.post("/webhook/pan/basic")
async def webhook_pan_basic(request: Request):
    """Webhook for basic PAN verification"""
    try:
        data = await request.json()
        logger.info(f"Received webhook request for basic PAN: {data}")
        
        # Extract PAN number from various possible field names
        pan_number = (
            data.get("pan_number") or 
            data.get("id_number") or 
            data.get("pan") or
            data.get("number")
        )
        
        if not pan_number:
            raise HTTPException(status_code=400, detail="PAN number not provided")
        
        # Call KYC server
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{KYC_SERVER_URL}/api/verify/pan/basic",
                json={"id_number": pan_number},
                timeout=30.0
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info("PAN verification successful")
                return result
            else:
                logger.error(f"KYC server error: {response.status_code}")
                raise HTTPException(status_code=response.status_code, detail=response.text)
                
    except httpx.RequestError as e:
        logger.error(f"Connection error to KYC server: {e}")
        raise HTTPException(status_code=503, detail="KYC server unavailable")
    except Exception as e:
        logger.error(f"Webhook error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/webhook/pan/comprehensive")
async def webhook_pan_comprehensive(request: Request):
    """Webhook for comprehensive PAN verification"""
    try:
        data = await request.json()
        logger.info(f"Received webhook request for comprehensive PAN: {data}")
        
        # Extract PAN number
        pan_number = (
            data.get("pan_number") or 
            data.get("id_number") or 
            data.get("pan") or
            data.get("number")
        )
        
        if not pan_number:
            raise HTTPException(status_code=400, detail="PAN number not provided")
        
        # Call KYC server
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{KYC_SERVER_URL}/api/verify/pan/comprehensive",
                json={"id_number": pan_number},
                timeout=30.0
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info("PAN comprehensive verification successful")
                return result
            else:
                logger.error(f"KYC server error: {response.status_code}")
                raise HTTPException(status_code=response.status_code, detail=response.text)
                
    except httpx.RequestError as e:
        logger.error(f"Connection error to KYC server: {e}")
        raise HTTPException(status_code=503, detail="KYC server unavailable")
    except Exception as e:
        logger.error(f"Webhook error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/webhook/pan/kra")
async def webhook_pan_kra(request: Request):
    """Webhook for PAN KRA verification"""
    try:
        data = await request.json()
        logger.info(f"Received webhook request for PAN KRA: {data}")
        
        # Extract PAN number
        pan_number = (
            data.get("pan_number") or 
            data.get("id_number") or 
            data.get("pan") or
            data.get("number")
        )
        
        if not pan_number:
            raise HTTPException(status_code=400, detail="PAN number not provided")
        
        # Call KYC server
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{KYC_SERVER_URL}/api/verify/pan/kra",
                json={"id_number": pan_number},
                timeout=30.0
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info("PAN KRA verification successful")
                return result
            else:
                logger.error(f"KYC server error: {response.status_code}")
                raise HTTPException(status_code=response.status_code, detail=response.text)
                
    except httpx.RequestError as e:
        logger.error(f"Connection error to KYC server: {e}")
        raise HTTPException(status_code=503, detail="KYC server unavailable")
    except Exception as e:
        logger.error(f"Webhook error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    port = int(os.getenv("WEBHOOK_PORT", "5678"))
    host = os.getenv("WEBHOOK_HOST", "127.0.0.1")
    
    logger.info(f"Starting webhook server on {host}:{port}")
    uvicorn.run(
        "simple_webhook_server:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )

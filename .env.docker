# KYC MCP Server Configuration
SUREPASS_API_TOKEN=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************.hXGP7wRAd6hZN12H_LaYQdTdUAXxfir5um3UYkSWGgs
SUREPASS_BASE_URL=https://kyc-api.surepass.io/api/v1

# Database Configuration
KYC_DATABASE_ENABLED=true
KYC_DATABASE_URL=sqlite+aiosqlite:///data/kyc_data.db
KYC_DATA_RETENTION_DAYS=365
KYC_MAX_SEARCH_RESULTS=100

# n8n Configuration
N8N_USER=admin
N8N_PASSWORD=admin123
N8N_HOST=localhost
TIMEZONE=UTC

# PostgreSQL Configuration (optional)
POSTGRES_DB=n8n
POSTGRES_USER=n8n
POSTGRES_PASSWORD=n8n123

# Server Configuration
PORT=8000
HOST=0.0.0.0

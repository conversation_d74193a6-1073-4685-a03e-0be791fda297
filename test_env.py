#!/usr/bin/env python3
"""Test script to verify environment variable loading"""

import os
import sys

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✓ Successfully loaded .env file")
except ImportError:
    print("✗ python-dotenv not installed")
    sys.exit(1)
except Exception as e:
    print(f"✗ Could not load .env file: {e}")
    sys.exit(1)

# Check if API token is loaded
api_token = os.getenv("SUREPASS_API_TOKEN")
if api_token:
    print(f"✓ API token loaded: {api_token[:20]}...")
    print(f"✓ Token length: {len(api_token)} characters")
else:
    print("✗ API token not found in environment variables")
    print("Available environment variables:")
    for key in sorted(os.environ.keys()):
        if 'SUREPASS' in key or 'KYC' in key:
            print(f"  {key}={os.environ[key][:20]}...")

print("\nEnvironment test completed!")

#!/usr/bin/env python3
"""Test connectivity to SurePass API"""

import asyncio
import httpx
import socket
import sys

async def test_connectivity():
    """Test different ways to connect to the API"""
    
    print("=== Testing SurePass API Connectivity ===\n")
    
    # Test 1: Basic DNS resolution
    print("1. Testing DNS resolution...")
    try:
        # Get IPv4 addresses only
        result = socket.getaddrinfo('kyc-api.surepass.io', 443, socket.AF_INET)
        ipv4_addresses = [addr[4][0] for addr in result]
        print(f"   ✓ IPv4 addresses: {ipv4_addresses}")
    except Exception as e:
        print(f"   ✗ DNS resolution failed: {e}")
        return
    
    # Test 2: Try connecting with different configurations
    configurations = [
        ("Default httpx client", {}),
        ("With longer timeout", {"timeout": 60.0}),
        ("IPv4 only (if supported)", {"timeout": 60.0}),
    ]
    
    for name, config in configurations:
        print(f"\n2. Testing {name}...")
        try:
            async with httpx.AsyncClient(**config) as client:
                # Try a simple HEAD request first
                response = await client.head("https://kyc-api.surepass.io/api/v1/", timeout=30.0)
                print(f"   ✓ HEAD request successful: {response.status_code}")
                
                # Try with your API token
                headers = {
                    "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************.hXGP7wRAd6hZN12H_LaYQdTdUAXxfir5um3UYkSWGgs",
                    "Content-Type": "application/json"
                }
                
                # Test actual API endpoint
                test_data = {"id_number": "**********"}
                api_response = await client.post(
                    "https://kyc-api.surepass.io/api/v1/pan/pan", 
                    json=test_data, 
                    headers=headers,
                    timeout=30.0
                )
                print(f"   ✓ API request successful: {api_response.status_code}")
                print(f"   Response: {api_response.text[:200]}...")
                
        except httpx.ConnectError as e:
            print(f"   ✗ Connection failed: {e}")
        except httpx.TimeoutException as e:
            print(f"   ✗ Timeout: {e}")
        except Exception as e:
            print(f"   ✗ Error: {e}")
    
    # Test 3: Try direct IP connection
    print(f"\n3. Testing direct IPv4 connection to {ipv4_addresses[0]}...")
    try:
        # Replace hostname with IP in URL
        direct_url = f"https://{ipv4_addresses[0]}/api/v1/"
        async with httpx.AsyncClient(verify=False, timeout=30.0) as client:  # Disable SSL verification for IP
            response = await client.head(direct_url)
            print(f"   ✓ Direct IP connection successful: {response.status_code}")
    except Exception as e:
        print(f"   ✗ Direct IP connection failed: {e}")
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    asyncio.run(test_connectivity())
